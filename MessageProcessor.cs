using Hl7Converter.Services;
using Microsoft.Extensions.Logging;

namespace Hl7Converter;

/// <summary>
/// Handles HL7 message processing operations
/// </summary>
public class MessageProcessor
{
    private readonly ILogger<MessageProcessor> _logger;

    public MessageProcessor(ILogger<MessageProcessor> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Processes HL7 messages from Azure Blob Storage
    /// </summary>
    /// <param name="enhancementService">The HL7 enhancement service</param>
    /// <param name="blobSimulator">The blob trigger simulator</param>
    /// <returns>Processing summary with counts</returns>
    public async Task<ProcessingSummary> ProcessFromBlobStorageAsync(
        Hl7EnhancementService enhancementService, 
        BlobTriggerSimulator blobSimulator)
    {
        _logger.LogInformation("Starting Azure Blob Storage processing");
        Console.WriteLine("\nProcessing HL7 messages from Azure Blob Storage...");

        var summary = new ProcessingSummary();

        try
        {
            await foreach (var (blobName, content) in blobSimulator.SimulateBlobTriggerAsync())
            {
                Console.WriteLine($"\nProcessing blob: {blobName}");

                var result = await enhancementService.ProcessMessageAsync(blobName, content);

                summary.ProcessedCount++;

                if (result.Status == "Success")
                {
                    if (result.WasEnhanced)
                    {
                        summary.EnhancedCount++;
                        Console.WriteLine($"✓ Enhanced: {blobName} (MRN: {result.ExtractedMrn})");
                    }
                    else
                    {
                        Console.WriteLine($"✓ Processed: {blobName} (MRN: {result.ExtractedMrn}) - No enhancement needed");
                    }
                }
                else
                {
                    summary.FailedCount++;
                    Console.WriteLine($"✗ Failed: {blobName} - {result.ErrorMessage}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing from blob storage");
            Console.WriteLine($"Error: {ex.Message}");
        }

        // Display summary
        Console.WriteLine("\n=== Processing Summary ===");
        Console.WriteLine($"Total processed: {summary.ProcessedCount}");
        Console.WriteLine($"Enhanced: {summary.EnhancedCount}");
        Console.WriteLine($"Failed: {summary.FailedCount}");

        return summary;
    }
}

/// <summary>
/// Summary of message processing results
/// </summary>
public class ProcessingSummary
{
    public int ProcessedCount { get; set; }
    public int EnhancedCount { get; set; }
    public int FailedCount { get; set; }
}
