using Azure.Storage.Blobs;
using Hl7Converter.Context;
using Hl7Converter.Models;
using Hl7Converter.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Hl7Converter;

/// <summary>
/// Handles dependency injection configuration for the HL7 Enhancement System
/// </summary>
public static class ServiceConfiguration
{
    /// <summary>
    /// Configures all services required for the HL7 Enhancement System
    /// </summary>
    /// <param name="services">The service collection to configure</param>
    /// <param name="configuration">The application configuration</param>
    public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Add logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddConfiguration(configuration.GetSection("Logging"));
        });

        // Add configuration
        services.AddSingleton(configuration);

        // Bind HL7 enhancement configuration
        var hl7Config = new Hl7EnhancementConfig();
        configuration.GetSection("Hl7Enhancement").Bind(hl7Config);
        services.AddSingleton(hl7Config);

        // Add Entity Framework
        services.AddDbContext<HmcLiveFeedContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("HmcLiveFeedsConnectionString")));

        // Add Azure Blob Storage
        var storageConnectionString = configuration.GetValue<string>("Values:AzureWebJobsStorage");
        if (!string.IsNullOrEmpty(storageConnectionString))
        {
            services.AddSingleton(new BlobServiceClient(storageConnectionString));
        }

        // Add application services
        services.AddScoped<PatientDataService>();
        services.AddScoped<Hl7MessageEnhancer>();
        services.AddScoped<Hl7EnhancementService>();

        // Add file storage service
        services.AddScoped<FileStorageService>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<FileStorageService>>();
            var config = provider.GetRequiredService<Hl7EnhancementConfig>();
            return new FileStorageService(logger, config.OriginalMessagesPath, config.EnhancedMessagesPath);
        });

        // Add blob trigger simulator
        services.AddScoped<BlobTriggerSimulator>(provider =>
        {
            var blobServiceClient = provider.GetService<BlobServiceClient>();
            var logger = provider.GetRequiredService<ILogger<BlobTriggerSimulator>>();
            var config = provider.GetRequiredService<Hl7EnhancementConfig>();

            if (blobServiceClient is not null)
            {
                return new BlobTriggerSimulator(blobServiceClient, logger, config.BlobContainerName);
            }

            // Return a mock implementation if no blob service client
            return new BlobTriggerSimulator(null!, logger, config.BlobContainerName);
        });

        // Add message processor
        services.AddScoped<MessageProcessor>();
    }
}
