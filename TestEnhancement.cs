using Hl7Converter.Models;
using Hl7Converter.Services;
using Microsoft.Extensions.Logging;

namespace Hl7Converter;

/// <summary>
/// Test class to demonstrate the enhanced HL7 message enhancement functionality
/// </summary>
public class TestEnhancement
{
    public static void TestStronglyTypedEnhancement()
    {
        // Create a logger factory for testing
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<Hl7MessageEnhancer>();
        
        // Create the enhancer
        var enhancer = new Hl7MessageEnhancer(logger);
        
        // Sample ADT_A04 message (based on the examples in the project)
        string sampleHl7 = @"MSH|^~\&|Millennium|HMC|RHAPSODY_ADT|HMC|20250313092759||ADT^A04|Q7486782230T13604061733|P|2.3||||||8859/1
EVN|A04|20250313092703|||039008^039008^<PERSON><PERSON><PERSON>^^^^^^DOCCNBR^Personnel^^^COMMUNITY DR NBR^""""
PID|1|HC08806997^^^MRN^MRN^""""|HC08806997^^^MRN^MR^""""|GHY6534^0^""""^Passport^Passport^""""|TEST^TEST BB BOY^^^^^official~^Test^^^^^usual||20040417|male||GCC National|^^^""""^^Qatar^Home^^""""~^^^""""^^""""^Birth^^""""||||Arabic|Single|""""|*********^^^NH^FIN NBR^""""|23045678912|||||||||Omani
PV1|1|Outpatient|HG Pulmonology^^^HG Hamad^^Ambulatory(s)^Main Bldg HG|""""|||EDPHYS^TEST^ED Physician^(Cerner)^^^^^External Id^Personnel^^^External Identifier^""""||||||||||||*********00000001^0^""""^^Visit Id||||||||||||||||||||HG Hamad||Active|||20250313092703
OBX|1|CE|COUNTRY_RES||Qatar";

        // Sample patient data
        var patient = new Patient
        {
            QidExpiryDate = DateTime.Parse("2025-12-31"),
            HcExpiryDate = DateTime.Parse("2026-06-30"),
            AssignedFamilyPhysicianId = "DR12345",
            AssignedHcCode = "HC001"
        };
        
        // Sample extracted fields (indicating what's already present)
        var extractedFields = new Hl7ExtractedFields
        {
            QatarIdExp = null, // Not present, should be added
            HcExpDate = null,  // Not present, should be added
            FamilyPhysician = null, // Not present, should be added
            PrimOrgName = null // Not present, should be added
        };
        
        Console.WriteLine("=== Original HL7 Message ===");
        Console.WriteLine(sampleHl7);
        Console.WriteLine();
        
        // Test the enhancement with next sequence number 2 (since we have 1 existing OBX)
        string enhancedHl7 = enhancer.EnhanceMessage(sampleHl7, patient, extractedFields, 2);
        
        Console.WriteLine("=== Enhanced HL7 Message ===");
        Console.WriteLine(enhancedHl7);
        Console.WriteLine();
        
        // Verify the enhancement
        if (!string.Equals(sampleHl7, enhancedHl7, StringComparison.Ordinal))
        {
            Console.WriteLine("✅ Message was successfully enhanced!");
            
            // Count OBX segments in enhanced message
            var obxCount = enhancedHl7.Split('\n').Count(line => line.StartsWith("OBX|"));
            Console.WriteLine($"📊 Total OBX segments in enhanced message: {obxCount}");
            
            // Show the new OBX segments
            var lines = enhancedHl7.Split('\n');
            Console.WriteLine("\n🆕 New OBX segments added:");
            for (int i = 0; i < lines.Length; i++)
            {
                if (lines[i].StartsWith("OBX|") && !lines[i].Contains("COUNTRY_RES"))
                {
                    Console.WriteLine($"   {lines[i]}");
                }
            }
        }
        else
        {
            Console.WriteLine("❌ No enhancement was performed");
        }
    }
}
