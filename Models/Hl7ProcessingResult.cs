namespace Hl7Converter;

public class Hl7ProcessingResult
{
    public string Filename { get; set; } = string.Empty;

    public string Status { get; set; } = string.Empty;

    public string? ErrorMessage { get; set; }

    public string? ExtractedMrn { get; set; }

    public Hl7ExtractedFields? ExtractedFields { get; set; }

    public bool WasEnhanced { get; set; }

    public string? OriginalFilePath { get; set; }

    public string? EnhancedFilePath { get; set; }

    public DateTime ProcessingStartTime { get; set; }

    public DateTime? ProcessingEndTime { get; set; }

    public TimeSpan? ProcessingDuration { get; set; }
}
