global using static System.Console;

using Azure.Storage.Blobs;
using Hl7Converter;
using Hl7Converter.Context;
using Hl7Converter.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

// Implements Azure Blob trigger simulation with database enhancement

WriteLine("=== HL7 Message Enhancement System ===");

// Build configuration
var configuration = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .Build();

// Setup dependency injection
var services = new ServiceCollection();
ServiceConfiguration.ConfigureServices(services, configuration);
var serviceProvider = services.BuildServiceProvider();

// Get logger
var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
logger.LogInformation("HL7 Enhancement System started");

try
{
    // Get main services
    var enhancementService = serviceProvider.GetRequiredService<Hl7EnhancementService>();
    var blobSimulator = serviceProvider.GetRequiredService<BlobTriggerSimulator>();
    var messageProcessor = serviceProvider.GetRequiredService<MessageProcessor>();

    // Check database connectivity
    var patientService = serviceProvider.GetRequiredService<PatientDataService>();
    var isDatabaseAvailable = await patientService.IsDatabaseAvailableAsync();

    if (isDatabaseAvailable)
    {
        logger.LogInformation("Database connection successful");
    }
    else
    {
        logger.LogWarning("Database connection failed - will process messages without enhancement");
    }

    // Process HL7 messages from Azure Blob Storage
    WriteLine("Processing HL7 messages from Azure Blob Storage...");
    var summary = await messageProcessor.ProcessFromBlobStorageAsync(enhancementService, blobSimulator);

    logger.LogInformation("Processing completed. Processed: {ProcessedCount}, Enhanced: {EnhancedCount}, Failed: {FailedCount}",
        summary.ProcessedCount, summary.EnhancedCount, summary.FailedCount);
}
catch (Exception ex)
{
    logger.LogError(ex, "Fatal error in application");
    WriteLine($"Fatal error: {ex.Message}");
}
finally
{
    await serviceProvider.DisposeAsync();
    WriteLine("\nPress any key to exit...");
    ReadKey();
}
