using Azure.Storage.Blobs;
using Microsoft.Extensions.Logging;
using System.Runtime.CompilerServices;

namespace Hl7Converter.Services;

/// <summary>
/// Simulates Azure Blob trigger functionality for the console application
/// </summary>
public class BlobTriggerSimulator(
    BlobServiceClient blobServiceClient,
    ILogger<BlobTriggerSimulator> logger,
    string containerName)
{
    /// <summary>
    /// Simulates blob trigger by polling for new HL7 files
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Enumerable of blob names and content</returns>
    public async IAsyncEnumerable<(string BlobName, string Content)> SimulateBlobTriggerAsync([EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting blob trigger simulation for container: {ContainerName}", containerName);

        BlobContainerClient? containerClient;
        bool containerExists;

        try
        {
            containerClient = blobServiceClient.GetBlobContainerClient(containerName);
            containerExists = await containerClient.ExistsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error accessing blob container");
            yield break;
        }

        if (!containerExists)
        {
            logger.LogWarning("Container {ContainerName} does not exist", containerName);
            yield break;
        }

        // List all blobs in the container
        await foreach (var blobItem in GetBlobsAsync(containerClient, cancellationToken))
        {
            if (cancellationToken.IsCancellationRequested)
                yield break;

            // Filter for HL7 files (typically .hl7 extension)
            if (!IsHl7File(blobItem.Name)) continue;
            var content = await GetBlobContentAsync(containerClient, blobItem.Name, cancellationToken);
            if (content != null)
            {
                yield return (blobItem.Name, content);
            }
        }
    }

    private static async IAsyncEnumerable<Azure.Storage.Blobs.Models.BlobItem> GetBlobsAsync(BlobContainerClient containerClient, [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        await foreach (var blobItem in containerClient.GetBlobsAsync(cancellationToken: cancellationToken))
        {
            yield return blobItem;
        }
    }

    private async Task<string?> GetBlobContentAsync(BlobContainerClient containerClient, string blobName, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Processing blob: {BlobName}", blobName);

            var blobClient = containerClient.GetBlobClient(blobName);
            var response = await blobClient.DownloadContentAsync(cancellationToken);

            return response.Value.Content.ToString();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing blob: {BlobName}", blobName);
            return null;
        }
    }

    /// <summary>
    /// Determines if a file is an HL7 file based on its name/extension
    /// </summary>
    private static bool IsHl7File(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension == ".hl7" || extension == ".txt" || 
               fileName.Contains("hl7", StringComparison.InvariantCultureIgnoreCase) ||
               fileName.Contains("adt", StringComparison.InvariantCultureIgnoreCase);
    }
}
