using Hl7Converter.Context;
using Hl7Converter.Models;
using Microsoft.Extensions.Logging;
using NHapi.Base.Model;
using NHapi.Base.Parser;
using NHapi.Base.Util;

namespace Hl7Converter.Services;

/// <summary>
/// Service for enhancing HL7 messages with missing OBX segments using the nHapi library.
/// It parses HL7 messages, identifies missing required OBX segments based on patient data,
/// adds them, and then re-encodes the message.
/// </summary>
public class Hl7MessageEnhancer(ILogger<Hl7MessageEnhancer> logger)
{
    private readonly PipeParser _pipeParser = new(); // nHapi's standard parser for ER7 (pipe-and-hat) encoded messages.

    private const string DateFormat = "yyyyMMdd"; // Standard HL7 date format.
    private const string DefaultObservationResultStatus = "F"; // Default status for new OBX segments (F = Final).

    /// <summary>
    /// Enhances an HL7 message string by adding missing OBX segments.
    /// This method uses an improved nHapi Terser approach for robust parsing and manipulation.
    /// </summary>
    /// <param name="originalHl7">The original HL7 message content as a string.</param>
    /// <param name="patient">The Patient object containing data to populate new OBX segments.</param>
    /// <param name="extractedFields">An object indicating which of the target OBX fields might already exist in the message.</param>
    /// <returns>The enhanced HL7 message as a string. Returns the original message if parsing fails or if an error occurs during enhancement.</returns>
    public string EnhanceMessage(string originalHl7, Patient patient, Hl7ExtractedFields extractedFields)
    {
        logger.LogInformation("Starting HL7 message enhancement using improved nHapi Terser approach");
        IMessage message;

        try
        {
            message = _pipeParser.Parse(originalHl7);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to parse original HL7 message with nHapi. Returning original message");
            return originalHl7;
        }

        try
        {
            var messageType = GetMessageType(message);
            logger.LogInformation("Detected message type: {MessageType}", messageType);

            int addedSegments = EnhanceMessageWithTerser(message, patient, extractedFields);

            if (addedSegments > 0)
            {
                logger.LogInformation("HL7 message enhancement completed. Added {Count} OBX segments", addedSegments);
                try
                {
                    return _pipeParser.Encode(message);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to encode enhanced message. Returning original");
                    return originalHl7;
                }
            }

            logger.LogInformation(
                "No new OBX segments were added to the HL7 message as conditions were not met or data was already present");
            return originalHl7;
        }
        catch (Exception ex)
        {
            logger.LogError(ex,
                "Error during the enhancement phase of HL7 message processing with improved nHapi Terser. Returning original message");
            return originalHl7;
        }
    }

    /// <summary>
    /// Gets the message type from the MSH segment
    /// </summary>
    private string GetMessageType(IMessage message)
    {
        try
        {
            var terser = new Terser(message);
            var messageType = terser.Get("/MSH-9-1");
            var triggerEvent = terser.Get("/MSH-9-2");
            return $"{messageType}_{triggerEvent}";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting message type from MSH segment");
            return "UNKNOWN";
        }
    }

    /// <summary>
    /// Enhanced message processing using an improved Terser approach
    /// </summary>
    private int EnhanceMessageWithTerser(IMessage message, Patient patient, Hl7ExtractedFields extractedFields)
    {
        try
        {
            var terser = new Terser(message);
            int addedSegments = 0;

            // Count existing OBX segments more reliably
            int obxCount = CountExistingObxSegments(terser);
            logger.LogInformation("Found {Count} existing OBX segments", obxCount);

            // Try to add each missing segment
            addedSegments += TryAddObxSegmentImproved(terser, extractedFields.QatarIdExp, patient.QidExpiryDate?.ToString(DateFormat),
                "DT", "QATAR_ID_EXP", obxCount + addedSegments);

            addedSegments += TryAddObxSegmentImproved(terser, extractedFields.HcExpDate, patient.HcExpiryDate?.ToString(DateFormat),
                "DT", "HC EXP DATE", obxCount + addedSegments);

            addedSegments += TryAddObxSegmentImproved(terser, extractedFields.FamilyPhysician, patient.AssignedFamilyPhysicianId,
                "ST", "FAMILY_PHYSICIAN", obxCount + addedSegments);

            addedSegments += TryAddObxSegmentImproved(terser, extractedFields.PrimOrgName, patient.AssignedHcCode,
                "TX", "PRIM_ORG_NAME", obxCount + addedSegments);

            return addedSegments;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error enhancing message with improved Terser approach");
            return 0;
        }
    }

    /// <summary>
    /// Counts existing OBX segments more reliably
    /// </summary>
    private static int CountExistingObxSegments(Terser terser)
    {
        int obxCount = 0;
        const int maxObxToCheck = 100; // Increased safety limit

        while (obxCount < maxObxToCheck)
        {
            try
            {
                var obxPath = $"/OBX({obxCount})";
                var segment = terser.GetSegment(obxPath);
                if (segment == null)
                    break;
                obxCount++;
            }
            catch
            {
                break;
            }
        }

        return obxCount;
    }

    /// <summary>
    /// Improved method to add OBX segment using Terser with better error handling
    /// </summary>
    private int TryAddObxSegmentImproved(Terser terser, string? existingValue, string? value,
        string valueType, string identifier, int obxIndex)
    {
        if (!string.IsNullOrEmpty(existingValue) || string.IsNullOrEmpty(value)) return 0;

        try
        {
            var obxPath = $"/OBX({obxIndex})";
            var sequenceNumber = obxIndex + 1;

            // Create the segment by setting its fields
            // This approach is more reliable than trying to get a template segment
            terser.Set($"{obxPath}-1", sequenceNumber.ToString());
            terser.Set($"{obxPath}-2", valueType);
            terser.Set($"{obxPath}-3-1", identifier);
            terser.Set($"{obxPath}-5", value);
            terser.Set($"{obxPath}-11", DefaultObservationResultStatus);

            logger.LogInformation("Added {Identifier} OBX segment: {Value}", identifier, value);
            return 1;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to add OBX segment for {Identifier}", identifier);
            return 0;
        }
    }

}